[gd_scene load_steps=6 format=3 uid="uid://dbtiid3jqxfis"]

[ext_resource type="Texture2D" uid="uid://clefimr5y1rb0" path="res://Foret/bush3.png" id="1_35i0e"]
[ext_resource type="Script" uid="uid://bxwxofcy8kqno" path="res://articulation.gd" id="1_qynqj"]
[ext_resource type="Texture2D" uid="uid://dcftvyt413tob" path="res://Foret/pillar1.png" id="2_qynqj"]
[ext_resource type="Texture2D" uid="uid://hpjycabnu2ke" path="res://Foret/pillar2.png" id="3_76rj8"]
[ext_resource type="Texture2D" uid="uid://csnih1m6dtcpv" path="res://Foret/m_pillar5.png" id="4_envkd"]

[node name="Articulation" type="Node2D"]
script = ExtResource("1_qynqj")

[node name="Corp" type="Sprite2D" parent="."]
texture = ExtResource("1_35i0e")

[node name="epaule droit" type="Sprite2D" parent="Corp"]
position = Vector2(31.7144, 4.01671)
rotation = 0.158261
scale = Vector2(0.190498, 0.327181)
texture = ExtResource("2_qynqj")
offset = Vector2(211.947, -47.8075)

[node name="bras droit" type="Sprite2D" parent="Corp/epaule droit"]
position = Vector2(395.204, -82.5742)
scale = Vector2(1.51683, 0.875648)
texture = ExtResource("2_qynqj")
offset = Vector2(211.947, -47.8075)

[node name="epaule gauche" type="Sprite2D" parent="Corp"]
position = Vector2(-25.4653, 2.99013)
rotation = -2.68037
scale = Vector2(0.168612, 0.357469)
texture = ExtResource("2_qynqj")
offset = Vector2(221.82, -55.4361)

[node name="bras gauche" type="Sprite2D" parent="Corp/epaule gauche"]
position = Vector2(412.868, -100.902)
scale = Vector2(1.39497, 1.35868)
texture = ExtResource("2_qynqj")
offset = Vector2(221.82, -55.4361)

[node name="cuisse droit" type="Sprite2D" parent="Corp"]
position = Vector2(24.3994, 21.8704)
rotation = -1.71535
scale = Vector2(0.246799, 0.282309)
texture = ExtResource("3_76rj8")
offset = Vector2(-238.796, 59.6684)

[node name="jambe droit" type="Sprite2D" parent="Corp/cuisse droit"]
position = Vector2(-369.478, 26.7429)
texture = ExtResource("3_76rj8")
offset = Vector2(-238.796, 59.6684)

[node name="pied droit" type="Sprite2D" parent="Corp/cuisse droit/jambe droit"]
position = Vector2(-368.343, 42.8013)
rotation = 1.49728
scale = Vector2(2.18699, 1.58844)
skew = 0.0172881
texture = ExtResource("4_envkd")
offset = Vector2(34.795, 40.6691)

[node name="cuisse gauche" type="Sprite2D" parent="Corp"]
position = Vector2(-14.6223, 29.7576)
rotation = -1.7604
scale = Vector2(0.270483, -0.288428)
texture = ExtResource("3_76rj8")
offset = Vector2(-239.385, 48.458)

[node name="jambe gauche" type="Sprite2D" parent="Corp/cuisse gauche"]
position = Vector2(-377.233, 37.4517)
scale = Vector2(0.806311, 0.820503)
texture = ExtResource("3_76rj8")
offset = Vector2(-239.385, 48.458)

[node name="pied gauche" type="Sprite2D" parent="Corp/cuisse gauche/jambe gauche"]
position = Vector2(-387.759, 21.2494)
rotation = -1.55792
scale = Vector2(2.62013, -1.60288)
skew = -0.00194085
texture = ExtResource("4_envkd")
offset = Vector2(-34.4047, 38.2853)
