extends Node2D

var tour_complet := 2*PI
var new_angle := 0.
@export var angle_amplitude := PI /18
@export var angle_periode := .3

var random_angle_amplitude := PI /30
var random_angle_periode := .7

var variation_angle_amplitude := 0.
var variation_angle_periode := 0.

func _ready() -> void:
    randomize()
    angle_amplitude += randf_range(-random_angle_amplitude, random_angle_amplitude)
    angle_periode += randf_range(-random_angle_periode, random_angle_periode)


func _process(delta: float) -> void:
    var temps_courrant_seconde = Time.get_ticks_msec() / 1000.0

    new_angle = (angle_amplitude+variation_angle_amplitude) * sin( tour_complet * temps_courrant_seconde / angle_periode )
    rotation = new_angle 
    