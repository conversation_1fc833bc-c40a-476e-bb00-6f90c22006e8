[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://b6v7u723puvqu"
path="res://.godot/imported/m_pillar1.png-8c7ec3a2d1159a2f68400753ecf40dc5.ctex"
metadata={
"vram_texture": false
}

[deps]

source_file="res://Foret/m_pillar1.png"
dest_files=["res://.godot/imported/m_pillar1.png-8c7ec3a2d1159a2f68400753ecf40dc5.ctex"]

[params]

compress/mode=0
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=0
compress/channel_pack=0
mipmaps/generate=false
mipmaps/limit=-1
roughness/mode=0
roughness/src_normal=""
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=1
