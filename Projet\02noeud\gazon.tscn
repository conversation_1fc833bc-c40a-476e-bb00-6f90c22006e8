[gd_scene load_steps=5 format=3 uid="uid://2y26hb7n44jo"]

[ext_resource type="Texture2D" uid="uid://b5ir1cp682eo2" path="res://Foret/plant1.png" id="1_cils7"]
[ext_resource type="Script" uid="uid://dfex50vqn5awk" path="res://Oscillation.gd" id="2_qea54"]
[ext_resource type="Texture2D" uid="uid://csxtietsgklye" path="res://Foret/plant2.png" id="2_u7k7a"]
[ext_resource type="Texture2D" uid="uid://dqaop3e41rrct" path="res://Foret/plant3.png" id="3_rn8yi"]

[node name="Gazon" type="Node2D"]

[node name="Plant1" type="Sprite2D" parent="."]
position = Vector2(-29, 5)
texture = ExtResource("1_cils7")
offset = Vector2(10, -45)
script = ExtResource("2_qea54")

[node name="Plant2" type="Sprite2D" parent="."]
position = Vector2(-71, 0)
rotation = -0.0170008
texture = ExtResource("2_u7k7a")
offset = Vector2(-2, -42)
script = ExtResource("2_qea54")

[node name="Plant3" type="Sprite2D" parent="."]
position = Vector2(38, -2)
texture = ExtResource("3_rn8yi")
offset = Vector2(12, -51)
script = ExtResource("2_qea54")
